import React from 'react';
import { motion } from 'framer-motion';
import { Star, Quote } from 'lucide-react';

const Testimonials = () => {
  const testimonials = [
    {
      name: "<PERSON>",
      role: "Homeowner <PERSON> <PERSON><PERSON>",
      image: "https://images.pexels.com/photos/1516680/pexels-photo-1516680.jpeg?auto=compress&cs=tinysrgb&w=150",
      rating: 5,
      content: "The level of quality and attention to detail in our home is exceptional. Soldiers Builders truly delivers on their promise of luxury living. The construction quality, finishing work, and timely delivery exceeded our expectations. We're proud to call this our home.",
      project: "<PERSON><PERSON>",
      date: "March 2024"
    },
    {
      name: "<PERSON>",
      role: "Property Investor",
      image: "https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg?auto=compress&cs=tinysrgb&w=150",
      rating: 5,
      content: "Working with Soldiers Builders was a seamless experience. Their military precision in execution is evident in every aspect of the project. The transparency in communication and adherence to timelines made this investment decision easy and rewarding.",
      project: "<PERSON><PERSON><PERSON>",
      date: "February 2024"
    },
    {
      name: "Dr. <PERSON>",
      role: "Landowner Partner",
      image: "https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=150",
      rating: 5,
      content: "The transparency and professionalism shown throughout our joint venture exceeded our expectations. Soldiers Builders handled everything from design to completion with remarkable efficiency. The profit-sharing model is fair and transparent.",
      project: "Kashful",
      date: "January 2024"
    },
    {
      name: "Fatima Begum",
      role: "Homeowner - Harun's Nest",
      image: "https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=150",
      rating: 5,
      content: "From the initial consultation to the final handover, every step was handled with care and professionalism. The quality of materials used and the craftsmanship is outstanding. Our family is extremely happy with our new home.",
      project: "Harun's Nest",
      date: "December 2023"
    },
    {
      name: "Rashid Ahmed",
      role: "Business Owner",
      image: "https://images.pexels.com/photos/1559486/pexels-photo-1559486.jpeg?auto=compress&cs=tinysrgb&w=150",
      rating: 5,
      content: "I was impressed by the military-level discipline in their project management. Every deadline was met, every promise was kept. The architectural design and construction quality reflect their commitment to excellence.",
      project: "Tilottoma",
      date: "November 2023"
    },
    {
      name: "Nasreen Sultana",
      role: "Homeowner",
      image: "https://images.pexels.com/photos/1181424/pexels-photo-1181424.jpeg?auto=compress&cs=tinysrgb&w=150",
      rating: 5,
      content: "The interior design and space utilization is exceptional. Every room feels spacious and well-planned. The premium features that come standard with Soldiers Builders projects make it excellent value for money.",
      project: "Mollick's Dream",
      date: "October 2023"
    }
  ];

  const stats = [
    { number: "500+", label: "Happy Families" },
    { number: "4.9/5", label: "Average Rating" },
    { number: "98%", label: "Satisfaction Rate" },
    { number: "100%", label: "Referral Rate" }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-48 bg-gradient-to-r from-gray-900 to-gray-800">
        <div className="absolute inset-0">
          <img
            src="/assets/images/projects/File/gvcbvvnb.png"
            alt="Testimonials Background"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-black/40"></div>
        </div>
      </section>

      {/* Title and Subtitle after Hero Section */}
      <section className="bg-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-black mb-6">
            Client <span className="text-green-800">Testimonials</span>
          </h1>
          <p className="text-xl text-gray-700 max-w-3xl mx-auto">
            Hear from our satisfied clients who have experienced the Soldiers Builders difference
          </p>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="text-center"
              >
                <div className="text-3xl md:text-4xl font-bold text-yellow-400 mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-400">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Grid */}
      <section className="py-20 bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -5 }}
                className="bg-gray-900 rounded-xl p-6 hover:bg-gray-700 transition-all duration-300 relative"
              >
                <div className="absolute top-4 right-4">
                  <Quote className="h-8 w-8 text-yellow-400 opacity-20" />
                </div>
                
                <div className="flex items-center mb-4">
                  <img
                    src={testimonial.image}
                    alt={testimonial.name}
                    className="w-16 h-16 rounded-full mr-4 object-cover"
                  />
                  <div>
                    <h3 className="text-white font-bold text-lg">{testimonial.name}</h3>
                    <p className="text-gray-400 text-sm">{testimonial.role}</p>
                  </div>
                </div>

                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                  ))}
                </div>

                <p className="text-gray-300 mb-4 leading-relaxed">
                  "{testimonial.content}"
                </p>

                <div className="flex items-center justify-between text-sm text-gray-500">
                  <span>{testimonial.project}</span>
                  <span>{testimonial.date}</span>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Video Testimonials */}
      <section className="py-20 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Video Testimonials
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Watch our clients share their experiences in their own words
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Coming Soon Placeholder */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-gray-800 rounded-xl p-8 text-center border-2 border-dashed border-gray-600"
            >
              <div className="bg-yellow-400/20 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="h-10 w-10 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Client Interview #1</h3>
              <p className="text-gray-400 mb-6 text-sm">
                Ahmed Rahman shares his experience building with Soldiers Builders
              </p>
              <div className="bg-gray-700 text-yellow-400 px-4 py-2 rounded-lg text-sm font-medium">
                Coming Soon
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.1 }}
              className="bg-gray-800 rounded-xl p-8 text-center border-2 border-dashed border-gray-600"
            >
              <div className="bg-yellow-400/20 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="h-10 w-10 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Project Handover</h3>
              <p className="text-gray-400 mb-6 text-sm">
                Key handover ceremony at Bondhu Bilash project
              </p>
              <div className="bg-gray-700 text-yellow-400 px-4 py-2 rounded-lg text-sm font-medium">
                Coming Soon
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="bg-gray-800 rounded-xl p-8 text-center border-2 border-dashed border-gray-600"
            >
              <div className="bg-yellow-400/20 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="h-10 w-10 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Construction Journey</h3>
              <p className="text-gray-400 mb-6 text-sm">
                Time-lapse of construction progress at Tilottoma
              </p>
              <div className="bg-gray-700 text-yellow-400 px-4 py-2 rounded-lg text-sm font-medium">
                Coming Soon
              </div>
            </motion.div>
          </div>

          {/* Video Upload Notice */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="mt-12 bg-gradient-to-r from-yellow-400/10 to-yellow-600/10 border border-yellow-400/30 rounded-xl p-8 text-center"
          >
            <h3 className="text-2xl font-bold text-white mb-4">Video Testimonials Coming Soon!</h3>
            <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
              We're currently working on producing high-quality video testimonials featuring our satisfied clients.
              These videos will showcase real experiences and genuine feedback from families who have built their dreams with us.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-yellow-400 text-gray-900 px-6 py-3 rounded-lg font-semibold hover:bg-yellow-500 transition-all duration-200"
              >
                📹 Subscribe for Updates
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="border-2 border-yellow-400 text-yellow-400 px-6 py-3 rounded-lg font-semibold hover:bg-yellow-400 hover:text-gray-900 transition-all duration-200"
              >
                📞 Share Your Story
              </motion.button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* New Messages */}
      <section className="py-20 bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Latest Client Messages
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Fresh testimonials and messages from our recent clients
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Placeholder for new messages */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-gray-900 rounded-xl p-8 border-2 border-dashed border-gray-600 text-center"
            >
              <div className="bg-yellow-400/20 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="h-8 w-8 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">Recent Feedback</h3>
              <p className="text-gray-400 mb-6">
                New testimonials and feedback from our latest project completions will appear here.
              </p>
              <div className="bg-gray-700 text-yellow-400 px-4 py-2 rounded-lg text-sm font-medium inline-block">
                Updates Coming Soon
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-gray-900 rounded-xl p-8 border-2 border-dashed border-gray-600 text-center"
            >
              <div className="bg-yellow-400/20 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="h-8 w-8 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">Client Reviews</h3>
              <p className="text-gray-400 mb-6">
                Written reviews and detailed feedback from families who have recently moved into their new homes.
              </p>
              <div className="bg-gray-700 text-yellow-400 px-4 py-2 rounded-lg text-sm font-medium inline-block">
                Updates Coming Soon
              </div>
            </motion.div>
          </div>

          {/* Call to Action for New Messages */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="mt-12 bg-gradient-to-r from-green-800/20 to-green-600/20 border border-green-400/30 rounded-xl p-8 text-center"
          >
            <h3 className="text-2xl font-bold text-white mb-4">Share Your Experience</h3>
            <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
              Are you a Soldiers Builders client? We'd love to hear about your experience!
              Your feedback helps us improve and helps future clients make informed decisions.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-all duration-200"
              >
                ✍️ Write a Review
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="border-2 border-green-400 text-green-400 px-6 py-3 rounded-lg font-semibold hover:bg-green-400 hover:text-gray-900 transition-all duration-200"
              >
                📧 Send Feedback
              </motion.button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Success Stories */}
      <section className="py-20 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Success Stories
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Real stories from real people who chose Soldiers Builders for their dreams
            </p>
          </motion.div>

          <div className="space-y-12">
            {[
              {
                title: "From Dream to Reality",
                client: "The Rahman Family",
                story: "After years of planning and saving, the Rahman family found their perfect home with Soldiers Builders. The journey from initial consultation to key handover was seamless, and today they enjoy their dream home in Bondhu Bilash.",
                image: "https://images.pexels.com/photos/1516680/pexels-photo-1516680.jpeg?auto=compress&cs=tinysrgb&w=400"
              },
              {
                title: "A Smart Investment Choice",
                client: "Dr. Mahmud",
                story: "As a busy professional, Dr. Mahmud needed a hassle-free investment opportunity. Soldiers Builders' transparent process and quality construction made the decision easy. His investment in Chondrima Bilash has exceeded expectations.",
                image: "https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=400"
              }
            ].map((story, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.2 }}
                className="bg-gray-900 rounded-xl p-8 flex flex-col md:flex-row items-center gap-8"
              >
                <img
                  src={story.image}
                  alt={story.client}
                  className="w-48 h-48 rounded-xl object-cover"
                />
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-white mb-2">{story.title}</h3>
                  <p className="text-yellow-400 font-medium mb-4">{story.client}</p>
                  <p className="text-gray-300 leading-relaxed">{story.story}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-green-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white">
            Ready to Join Our Happy Clients?
          </h2>
          <p className="text-xl text-white max-w-2xl mx-auto">
            Experience the Soldiers Builders difference for yourself. Start your journey today.
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mt-8">
            <button className="bg-white text-green-800 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-all duration-200">
              🏠 Start Your Journey
            </button>
            <button className="bg-white text-green-800 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-all duration-200">
              📝 Read More Reviews
            </button>
            <button className="bg-white text-green-800 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-all duration-200">
              📞 Call Now
            </button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Testimonials;