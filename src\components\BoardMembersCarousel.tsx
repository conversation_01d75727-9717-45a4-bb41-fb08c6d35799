"use client";

import React from "react";
import { Carousel, Card } from "./ui/apple-cards-carousel";

interface BoardMember {
  name: string;
  title: string;
  image: string;
  bio?: string;
  experience?: string;
  expertise?: string[];
}

interface BoardMembersCarouselProps {
  members: BoardMember[];
}

export function BoardMembersCarousel({ members }: BoardMembersCarouselProps) {
  const cards = members.map((member, index) => (
    <Card key={member.name} card={transformMemberToCard(member)} index={index} />
  ));

  return (
    <div className="w-full h-full py-20">
      <h2 className="max-w-7xl pl-4 mx-auto text-xl md:text-5xl font-bold text-white font-sans mb-4">
        Meet Our Distinguished Board
      </h2>
      <p className="max-w-7xl pl-4 mx-auto text-lg text-gray-400 font-sans mb-8">
        Leaders who guide Soldiers Builders with military precision and vision
      </p>
      <Carousel items={cards} />
    </div>
  );
}

const transformMemberToCard = (member: BoardMember) => {
  return {
    category: member.title,
    title: member.name,
    src: member.image,
    content: <MemberContent member={member} />,
  };
};

const MemberContent = ({ member }: { member: BoardMember }) => {
  return (
    <div className="bg-[#F5F5F7] dark:bg-neutral-800 p-8 md:p-14 rounded-3xl mb-4">
      <div className="flex flex-col md:flex-row gap-8 items-start">
        {/* Member Photo */}
        <div className="flex-shrink-0">
          <img
            src={member.image}
            alt={member.name}
            className="w-48 h-48 md:w-64 md:h-64 rounded-2xl object-cover shadow-lg"
          />
        </div>
        
        {/* Member Information */}
        <div className="flex-1">
          <h3 className="text-2xl md:text-4xl font-bold text-neutral-700 dark:text-neutral-200 mb-2">
            {member.name}
          </h3>
          <p className="text-lg md:text-xl text-yellow-600 dark:text-yellow-400 font-semibold mb-6">
            {member.title}
          </p>
          
          {member.bio && (
            <div className="mb-6">
              <h4 className="text-lg font-semibold text-neutral-700 dark:text-neutral-200 mb-3">
                Background
              </h4>
              <p className="text-neutral-600 dark:text-neutral-400 text-base md:text-lg leading-relaxed">
                {member.bio}
              </p>
            </div>
          )}
          
          {member.experience && (
            <div className="mb-6">
              <h4 className="text-lg font-semibold text-neutral-700 dark:text-neutral-200 mb-3">
                Experience
              </h4>
              <p className="text-neutral-600 dark:text-neutral-400 text-base md:text-lg leading-relaxed">
                {member.experience}
              </p>
            </div>
          )}
          
          {member.expertise && member.expertise.length > 0 && (
            <div className="mb-6">
              <h4 className="text-lg font-semibold text-neutral-700 dark:text-neutral-200 mb-3">
                Areas of Expertise
              </h4>
              <div className="flex flex-wrap gap-2">
                {member.expertise.map((skill, index) => (
                  <span
                    key={index}
                    className="px-3 py-1 bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 rounded-full text-sm font-medium"
                  >
                    {skill}
                  </span>
                ))}
              </div>
            </div>
          )}
          
          {/* Default content for members without detailed info */}
          {!member.bio && !member.experience && !member.expertise && (
            <div className="space-y-4">
              <p className="text-neutral-600 dark:text-neutral-400 text-base md:text-lg leading-relaxed">
                <span className="font-bold text-neutral-700 dark:text-neutral-200">
                  Distinguished Leadership.
                </span>{" "}
                As a valued member of our Board of Directors, {member.name} brings extensive experience 
                and strategic vision to guide Soldiers Builders in delivering exceptional residential developments.
              </p>
              
              <p className="text-neutral-600 dark:text-neutral-400 text-base md:text-lg leading-relaxed">
                Our board members combine military precision with business acumen to ensure every project 
                meets the highest standards of quality, integrity, and excellence that define the Soldiers Builders legacy.
              </p>
              
              <div className="bg-gradient-to-r from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 p-6 rounded-xl border-l-4 border-yellow-400">
                <p className="text-neutral-700 dark:text-neutral-300 font-medium italic">
                  "We build your dreams with the same dedication and precision we brought to serving our nation."
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
