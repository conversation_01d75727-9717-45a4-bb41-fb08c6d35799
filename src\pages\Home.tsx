import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ArrowRight, Award, Users, Building, Download, Calendar, Phone, ChevronLeft, ChevronRight } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useTheme } from '../contexts/ThemeContext';
import AnimateOnce from '../components/AnimateOnce';
import FeaturedProjectsSlideshow from '../components/FeaturedProjectsSlideshow';

const Home = () => {
  const featuredProjects = [
    {
      id: 1,
      name: "Harun's Nest",
      slug: "haruns-nest",
      location: "Sector-16, Jolshiri Abashon, Dhaka",
      image: "/assets/images/projects/haruns-nest/IMG-20250517-WA0013.jpg",
      description: "Tranquil south-facing retreat near the lake",
      size: "2850 SFT",
      bedrooms: 4,
      status: "Ongoing"
    },
    {
      id: 2,
      name: "SAL Tilottoma",
      slug: "tilottoma",
      location: "Sector-11, Jolshiri Abashon",
      image: "/assets/images/projects/tilottoma/m-01_1 - Photo.jpg",
      description: "A tranquil residential haven, just a one-minute stroll from the lake",
      size: "2850 SFT",
      bedrooms: 4,
      status: "Ongoing"
    },
    {
      id: 3,
      name: "Mehnaz",
      slug: "mehnaz",
      location: "Sector-12, Jolshiri Abashon, Dhaka",
      image: "/assets/images/projects/mehnaz/WhatsApp Image 2025-05-28 at 15.44.17_a52b477d.jpg",
      description: "Elegantly positioned right beside the Central Mosque",
      size: "2850 SFT",
      bedrooms: 4,
      status: "Ongoing"
    },
    {
      id: 4,
      name: "Bondhu Bilash",
      slug: "bondhu-bilash",
      location: "Chondrima Model Town, Mohammadpur, Dhaka",
      image: "/assets/images/projects/bondhu-bilash/WhatsApp Image 2025-06-22 at 3.10.15 PM.jpeg",
      description: "We build your dreams",
      size: "2850 SFT",
      bedrooms: 4,
      status: "Completed"
    }
  ];





  // List of images from the File folder
  const heroImages = [
    '/assets/images/projects/File/vcbv.png',
    '/assets/images/projects/File/nbbmnm.png',
    '/assets/images/projects/File/gvcbvvnb.png',
    '/assets/images/projects/File/gbvbnbvbv.png',
    '/assets/images/projects/File/gbcv.png',
    '/assets/images/projects/File/bvnbvn.png',
    '/assets/images/projects/File/bvcbnbvbv.png',
    '/assets/images/projects/File/bvbnmbmb.png',
    '/assets/images/projects/File/bcvvnbv.png',
    '/assets/images/projects/File/bcvnbv.png',
  ];

  const heroTitles = [
    'Elevate Your Lifestyle',
    'Inspired Modern Living',
    'Where Vision Becomes Reality',
    'Designs for Tomorrow',
    'Experience Urban Luxury',
    'Crafted for Comfort',
    'A New Perspective on Home',
    'Live Above the Ordinary',
    'Your Dream, Our Blueprint',
    'Unmatched Elegance Awaits',
  ];

  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);
  const [direction, setDirection] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const { isDarkMode } = useTheme();

  // Animate text in with fade and slide up
  const AnimatedText = ({ text, className, delay = 0, perLetterDelay = 0.05, isTitle = false }: { text: string; className: string; delay?: number; perLetterDelay?: number; isTitle?: boolean }) => (
    <div className={className}>
      {text.split('').map((char, index) => (
        <motion.span
          key={index}
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            duration: 0.4,
            delay: delay + index * perLetterDelay,
            ease: 'easeOut',
          }}
          className="inline-block"
          style={{
            color: 'white',
            ...(isTitle ? {
              WebkitTextStroke: '0.5px black',
              textShadow: '0 2px 8px rgba(0,0,0,0.25)',
            } : {})
          }}
        >
          {char === ' ' ? '\u00A0' : char}
        </motion.span>
      ))}
    </div>
  );

  // Autoplay logic
  useEffect(() => {
    if (intervalRef.current) clearInterval(intervalRef.current);
    intervalRef.current = setInterval(() => {
      setDirection(1);
      setCurrentSlideIndex((prev) => (prev + 1) % heroImages.length);
    }, 6000);
    return () => intervalRef.current && clearInterval(intervalRef.current);
  }, [heroImages.length]);

  const goToSlide = (index: number) => {
    setDirection(index > currentSlideIndex ? 1 : -1);
    setCurrentSlideIndex(index);
  };

  const slideVariants = {
    initial: { opacity: 0, scale: 1.08 },
    animate: { opacity: 1, scale: 1, transition: { duration: 1.2, ease: 'easeInOut' } },
    exit: { opacity: 0, scale: 1.02, transition: { duration: 1.2, ease: 'easeInOut' } },
  };

  const contentVariants = {
    initial: { opacity: 0, y: 40 },
    animate: { opacity: 1, y: 0, transition: { duration: 0.8, ease: 'easeOut', staggerChildren: 0.15 } },
    exit: { opacity: 0, y: 40, transition: { duration: 0.5, ease: 'easeIn' } },
  };

  return (
    <div className="min-h-screen font-inter">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Background Slideshow */}
        <div className="absolute inset-0 z-0">
          <AnimatePresence initial={false} mode="wait">
            <motion.img
              key={heroImages[currentSlideIndex]}
              src={heroImages[currentSlideIndex]}
              alt="Hero Slide"
              variants={slideVariants}
              initial="initial"
              animate="animate"
              exit="exit"
              className="w-full h-full object-cover absolute inset-0"
              style={{ zIndex: 1 }}
            />
          </AnimatePresence>
          {/* Overlay for readability */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-transparent z-10" />
        </div>

        {/* Slide Indicators */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-30 flex space-x-3">
          {heroImages.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 border border-white/70 ${
                index === currentSlideIndex ? 'bg-white scale-125' : 'bg-white/40 hover:bg-white/70'
              }`}
            />
          ))}
        </div>

        {/* Hero Content */}
        <div className="relative z-20 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center flex flex-col items-center justify-center">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentSlideIndex}
              variants={contentVariants}
              initial="initial"
              animate="animate"
              exit="exit"
              className="space-y-8"
            >
              {/* Headline */}
              <div className="w-full text-center">
                <AnimatedText
                  text={heroTitles[currentSlideIndex % heroTitles.length]}
                  className="font-extrabold text-white drop-shadow-xl w-full text-center text-xl md:text-2xl lg:text-3xl"
                  delay={0.1}
                  perLetterDelay={0.04}
                  isTitle={true}
                  style={{ color: 'white' }}
                />
              </div>
              {/* Motto */}
              <motion.div
                initial={{ opacity: 0, y: 30, scale: 0.8 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ delay: 0.4, duration: 1.1, ease: 'easeOut' }}
                className="font-extrabold !text-white w-full text-center whitespace-nowrap text-[clamp(2rem,8vw,5rem)] md:text-[clamp(3rem,6vw,6rem)] lg:text-[clamp(4rem,5vw,7rem)]"
                style={{
                  color: 'white',
                  letterSpacing: '0.08em',
                  WebkitTextFillColor: 'white',
                  WebkitTextStroke: '0px',
                  MozTextFillColor: 'white',
                  MozTextStroke: '0px',
                  msTextFillColor: 'white',
                  msTextStroke: '0px',
                }}
              >
                We build your dreams
              </motion.div>
              {/* CTA Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 40 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7, duration: 0.7, ease: 'easeOut' }}
                className="flex flex-wrap justify-center gap-6 mt-6"
              >
                <a
                  href="/projects"
                  className="px-8 py-3 bg-white text-black font-semibold rounded-xl shadow-lg hover:-translate-y-1 hover:shadow-2xl transition-all duration-300 text-lg"
                >
                  Explore Projects
                </a>
                <a
                  href="tel:+8801769100680"
                  className="px-8 py-3 !bg-black !text-white font-semibold rounded-xl shadow-lg hover:-translate-y-1 hover:shadow-2xl transition-all duration-300 text-lg border border-white/30"
                  style={{ backgroundColor: 'black', color: 'white' }}
                >
                  Schedule Visit
                </a>
              </motion.div>
            </motion.div>
          </AnimatePresence>
        </div>
      </section>



      {/* Featured Projects Slideshow */}
      <FeaturedProjectsSlideshow featuredProjects={featuredProjects} />

      {/* Quick Links */}
      <section className="py-16 bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Link to="/downloads">
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="bg-gray-900 p-6 rounded-xl text-center hover:bg-gray-700 transition-all duration-200 cursor-pointer"
              >
                <Download className="h-8 w-8 text-yellow-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">Download Brochure</h3>
                <p className="text-gray-400 text-sm">Get detailed information about our projects</p>
              </motion.div>
            </Link>
            <Link to="/contact">
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="bg-gray-900 p-6 rounded-xl text-center hover:bg-gray-700 transition-all duration-200 cursor-pointer"
              >
                <Calendar className="h-8 w-8 text-yellow-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">Book Now</h3>
                <p className="text-gray-400 text-sm">Schedule a visit to our projects</p>
              </motion.div>
            </Link>
            <Link to="/contact">
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="bg-gray-900 p-6 rounded-xl text-center hover:bg-gray-700 transition-all duration-200 cursor-pointer"
              >
                <Phone className="h-8 w-8 text-yellow-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">Contact Us</h3>
                <p className="text-gray-400 text-sm">Get in touch with our team</p>
              </motion.div>
            </Link>
            <Link to="/gallery">
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="bg-gray-900 p-6 rounded-xl text-center hover:bg-gray-700 transition-all duration-200 cursor-pointer"
              >
                <Building className="h-8 w-8 text-yellow-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">View Gallery</h3>
                <p className="text-gray-400 text-sm">Explore our project galleries</p>
              </motion.div>
            </Link>
          </div>
        </div>
      </section>

      {/* Client Testimonials CTA */}
      <section className="py-20 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <AnimateOnce
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              What Our Clients Say
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto mb-12">
              Hear from our satisfied clients who have experienced the Soldiers Builders difference
            </p>

            {/* Preview Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-12">
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-yellow-400 mb-2">500+</div>
                <div className="text-gray-400">Happy Families</div>
              </div>
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-yellow-400 mb-2">4.9/5</div>
                <div className="text-gray-400">Average Rating</div>
              </div>
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-yellow-400 mb-2">98%</div>
                <div className="text-gray-400">Satisfaction Rate</div>
              </div>
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-yellow-400 mb-2">100%</div>
                <div className="text-gray-400">Referral Rate</div>
              </div>
            </div>

            {/* CTA Button */}
            <Link to="/testimonials">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-gradient-to-r from-yellow-400 to-yellow-600 text-gray-900 px-12 py-4 rounded-xl font-bold text-lg hover:from-yellow-500 hover:to-yellow-700 transition-all duration-200 shadow-lg flex items-center space-x-3 mx-auto"
              >
                <Users className="h-6 w-6" />
                <span>Read All Client Stories</span>
                <ArrowRight className="h-6 w-6" />
              </motion.button>
            </Link>

            {/* Quick Preview */}
            <div className="mt-12 bg-gray-800 rounded-2xl p-8 max-w-4xl mx-auto">
              <div className="flex items-center justify-center mb-6">
                <div className="flex -space-x-4">
                  <img
                    src="https://images.pexels.com/photos/1516680/pexels-photo-1516680.jpeg?auto=compress&cs=tinysrgb&w=150"
                    alt="Client"
                    className="w-12 h-12 rounded-full border-4 border-gray-800"
                  />
                  <img
                    src="https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg?auto=compress&cs=tinysrgb&w=150"
                    alt="Client"
                    className="w-12 h-12 rounded-full border-4 border-gray-800"
                  />
                  <img
                    src="https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=150"
                    alt="Client"
                    className="w-12 h-12 rounded-full border-4 border-gray-800"
                  />
                  <div className="w-12 h-12 rounded-full bg-yellow-400 border-4 border-gray-800 flex items-center justify-center">
                    <span className="text-gray-900 font-bold text-sm">+50</span>
                  </div>
                </div>
              </div>
              <p className="text-gray-300 text-lg italic leading-relaxed">
                "The level of quality and attention to detail in our home is exceptional.
                Soldiers Builders truly delivers on their promise of luxury living..."
              </p>
              <p className="text-yellow-400 font-semibold mt-4">- Ahmed Rahman, Homeowner</p>
            </div>
          </AnimateOnce>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-green-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <AnimateOnce
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="space-y-6"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white">
              Ready to Build Your Dream?
            </h2>
            <p className="text-xl text-white max-w-2xl mx-auto">
              Join hundreds of satisfied clients who have trusted Soldiers Builders with their dreams
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mt-8">
              <motion.a
                href="tel:+8801769100680"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-white text-green-800 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-all duration-200 flex items-center space-x-2"
              >
                <Phone className="h-5 w-5" />
                <span>Call Now</span>
              </motion.a>
              <Link to="/contact">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-green-800 transition-all duration-200 flex items-center space-x-2"
                >
                  <Calendar className="h-5 w-5" />
                  <span>Schedule Visit</span>
                </motion.button>
              </Link>
            </div>
          </AnimateOnce>
        </div>
      </section>
    </div>
  );
};

export default Home;