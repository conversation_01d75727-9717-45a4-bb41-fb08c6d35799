import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ArrowLeft, Home, ZoomIn, X } from 'lucide-react';

const ProjectInterior = () => {
  const { projectId } = useParams();
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  // Project mapping for folder names
  const projectFolderMap: { [key: string]: string } = {
    'haruns-nest': 'Harun Nest',
    'mollicks-dream': "<PERSON><PERSON><PERSON>'s Dream",
    'tilottoma': 'SAL TILOTTOMA',
    'mehnaz': 'Mehnaz',
    'habibs-utopia': "Habib's Utopia",
    'bondhu-bilash': '<PERSON><PERSON>',
    'chandrima-bilash': 'Cho<PERSON><PERSON>',
    'kashful': 'Kashful',
    'chandra-neer': 'Chandra Neer',
    'lutfors-uttorayon': "Lutfor's Uttorayon"
  };

  // Project names for display
  const projectNames: { [key: string]: string } = {
    'haruns-nest': "Ha<PERSON>'s Nest",
    'mollicks-dream': "<PERSON><PERSON><PERSON>'s Dream",
    'tilottoma': 'SAL Tilottoma',
    'mehnaz': 'Mehnaz',
    'habibs-utopia': "Habib's Utopia",
    'bondhu-bilash': 'Bondhu Bilash',
    'chandrima-bilash': 'Chandrima Bilash',
    'kashful': 'Kashful',
    'chandra-neer': 'Chandra Neer',
    'lutfors-uttorayon': "Lutfor's Uttorayon"
  };

  // Interior images for each project
  const interiorImages: { [key: string]: string[] } = {
    'haruns-nest': [
      '/assets/images/interior ,floorplan, brochure/Harun Nest/Interior/1.png',
      '/assets/images/interior ,floorplan, brochure/Harun Nest/Interior/2.png',
      '/assets/images/interior ,floorplan, brochure/Harun Nest/Interior/3.png',
      '/assets/images/projects/File/slideshow new images/slideshow image 4.jpg',
      '/assets/images/interior ,floorplan, brochure/Harun Nest/Interior/vcbv.png'
    ],
    'mollicks-dream': [
      '/assets/images/interior ,floorplan, brochure/Mollick\'s Dream/interior/1.png',
      '/assets/images/interior ,floorplan, brochure/Mollick\'s Dream/interior/2.png',
      '/assets/images/interior ,floorplan, brochure/Mollick\'s Dream/interior/3.png',
      '/assets/images/projects/File/slideshow new images/slideshow image 5.jpg'
    ],
    'tilottoma': [
      '/assets/images/interior ,floorplan, brochure/SAL TILOTTOMA/Interior/1.png',
      '/assets/images/interior ,floorplan, brochure/SAL TILOTTOMA/Interior/2.png',
      '/assets/images/interior ,floorplan, brochure/SAL TILOTTOMA/Interior/3.png',
      '/assets/images/interior ,floorplan, brochure/SAL TILOTTOMA/Interior/bcvvnbv.png'
    ],
    'mehnaz': [
      '/assets/images/interior ,floorplan, brochure/Mehnaz/interior/1.png',
      '/assets/images/interior ,floorplan, brochure/Mehnaz/interior/2.png',
      '/assets/images/interior ,floorplan, brochure/Mehnaz/interior/3.png'
    ],
    'habibs-utopia': [
      '/assets/images/interior ,floorplan, brochure/Habib\'s Utopia/interior/1.png',
      '/assets/images/interior ,floorplan, brochure/Habib\'s Utopia/interior/2.png',
      '/assets/images/interior ,floorplan, brochure/Habib\'s Utopia/interior/3.png'
    ],
    'bondhu-bilash': [
      '/assets/images/interior ,floorplan, brochure/Bondhu Bilash/Interior/1.png',
      '/assets/images/interior ,floorplan, brochure/Bondhu Bilash/Interior/2.png',
      '/assets/images/interior ,floorplan, brochure/Bondhu Bilash/Interior/3.png',
      '/assets/images/interior ,floorplan, brochure/Bondhu Bilash/Interior/bvbnmbmb.png'
    ],
    'chandrima-bilash': [
      '/assets/images/interior ,floorplan, brochure/Chondrima Bilash/Interior/1.png',
      '/assets/images/interior ,floorplan, brochure/Chondrima Bilash/Interior/2.png',
      '/assets/images/interior ,floorplan, brochure/Chondrima Bilash/Interior/3.png'
    ],
    'kashful': [
      '/assets/images/interior ,floorplan, brochure/Kashful/interior/1.png',
      '/assets/images/interior ,floorplan, brochure/Kashful/interior/2.png',
      '/assets/images/interior ,floorplan, brochure/Kashful/interior/3.png'
    ],
    'chandra-neer': [
      '/assets/images/interior ,floorplan, brochure/extra (use it after instruction)/Chandra Neer/interior/1.png',
      '/assets/images/interior ,floorplan, brochure/extra (use it after instruction)/Chandra Neer/interior/2.png',
      '/assets/images/interior ,floorplan, brochure/extra (use it after instruction)/Chandra Neer/interior/3.png'
    ],
    'lutfors-uttorayon': [
      '/assets/images/interior ,floorplan, brochure/extra (use it after instruction)/Lutfor\'s Uttorayon/interior/1.png',
      '/assets/images/interior ,floorplan, brochure/extra (use it after instruction)/Lutfor\'s Uttorayon/interior/2.png',
      '/assets/images/interior ,floorplan, brochure/extra (use it after instruction)/Lutfor\'s Uttorayon/interior/3.png'
    ]
  };

  const projectName = projectNames[projectId as string] || 'Project';
  const images = interiorImages[projectId as string] || [];

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Hero Section */}
      <section className="relative py-32 bg-gradient-to-r from-gray-900 to-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <Link 
              to={`/projects/${projectId}`}
              className="inline-flex items-center text-yellow-400 hover:text-yellow-300 mb-6 transition-colors duration-200"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Project Details
            </Link>
            <div className="flex items-center mb-6">
              <Home className="h-8 w-8 text-yellow-400 mr-4" />
              <h1 className="text-4xl md:text-6xl font-bold text-white">
                Interior Design
              </h1>
            </div>
            <h2 className="text-2xl md:text-3xl text-gray-300 mb-4">
              {projectName}
            </h2>
            <p className="text-lg text-gray-400 max-w-3xl">
              Explore the luxurious interior designs and premium finishes that make this project exceptional. 
              Each space is carefully crafted to provide comfort, elegance, and modern living.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Interior Gallery */}
      <section className="py-16 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {images.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {images.map((image, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="relative group cursor-pointer"
                  onClick={() => setSelectedImage(image)}
                >
                  <div className="relative overflow-hidden rounded-xl bg-gray-800">
                    <img
                      src={image}
                      alt={`${projectName} Interior ${index + 1}`}
                      className="w-full h-64 object-cover transition-transform duration-300 group-hover:scale-105"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                      }}
                    />
                    <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                      <ZoomIn className="h-8 w-8 text-white" />
                    </div>
                  </div>
                  <div className="mt-4">
                    <h3 className="text-lg font-semibold text-white">
                      Interior View {index + 1}
                    </h3>
                    <p className="text-gray-400 text-sm">
                      Click to view full size
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="text-center py-16">
              <Home className="h-16 w-16 text-gray-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-400 mb-2">
                Interior Images Coming Soon
              </h3>
              <p className="text-gray-500">
                Interior design images for {projectName} will be available soon.
              </p>
            </div>
          )}
        </div>
      </section>

      {/* Image Modal */}
      {selectedImage && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4"
          onClick={() => setSelectedImage(null)}
        >
          <div className="relative max-w-4xl max-h-full">
            <button
              onClick={() => setSelectedImage(null)}
              className="absolute -top-12 right-0 text-white hover:text-gray-300 transition-colors"
            >
              <X className="h-8 w-8" />
            </button>
            <img
              src={selectedImage}
              alt="Interior Detail"
              className="max-w-full max-h-full object-contain rounded-lg"
            />
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default ProjectInterior;
