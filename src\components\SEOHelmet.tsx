import React from 'react';
import { Helmet } from 'react-helmet-async';

interface SEOHelmetProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: string;
  structuredData?: object;
}

const SEOHelmet: React.FC<SEOHelmetProps> = ({
  title = "Soldiers Builders - Premium Real Estate Developer in Dhaka, Bangladesh",
  description = "Soldiers Builders is Bangladesh's leading real estate developer in Dhaka. We build luxury apartments, commercial properties, and residential complexes with military precision and excellence. Trusted by 500+ families across Dhaka, Bangladesh.",
  keywords = "Soldiers Builders, real estate Bangladesh, real estate Dhaka, luxury apartments Dhaka, property developer Bangladesh, residential projects Dhaka, commercial real estate Bangladesh, apartment for sale Dhaka, property investment Bangladesh, construction company Dhaka, building developer Bangladesh, real estate company Dhaka, luxury housing Bangladesh, property development Dhaka, apartment complex Bangladesh",
  image = "https://soldiersbuilders.com/assets/images/og-image.jpg",
  url = "https://soldiersbuilders.com/",
  type = "website",
  structuredData
}) => {
  const baseUrl = "https://soldiersbuilders.com";
  const fullUrl = url.startsWith('http') ? url : `${baseUrl}${url}`;
  const fullImageUrl = image.startsWith('http') ? image : `${baseUrl}${image}`;

  return (
    <Helmet>
      {/* Primary Meta Tags */}
      <title>{title}</title>
      <meta name="title" content={title} />
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <link rel="canonical" href={fullUrl} />

      {/* Open Graph / Facebook */}
      <meta property="og:type" content={type} />
      <meta property="og:url" content={fullUrl} />
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={fullImageUrl} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:site_name" content="Soldiers Builders" />

      {/* Twitter */}
      <meta property="twitter:card" content="summary_large_image" />
      <meta property="twitter:url" content={fullUrl} />
      <meta property="twitter:title" content={title} />
      <meta property="twitter:description" content={description} />
      <meta property="twitter:image" content={fullImageUrl} />

      {/* Structured Data */}
      {structuredData && (
        <script type="application/ld+json">
          {JSON.stringify(structuredData)}
        </script>
      )}
    </Helmet>
  );
};

export default SEOHelmet;
