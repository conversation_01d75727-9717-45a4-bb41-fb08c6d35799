"use client";

import React from "react";
import { motion } from "framer-motion";
import { useN<PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { CardBody, CardContainer, CardItem } from "./ui/3d-card";
import { Home, FileText, Download } from "lucide-react";

interface ProjectDetailCardsProps {
  projectId: string;
}

interface Card {
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  image: string;
  buttonText: string;
  href: string;
  action: () => void;
}

export function ProjectDetailCards({ projectId }: ProjectDetailCardsProps) {
  const navigate = useNavigate();

  const handleBrochureDownload = (projectId: string) => {
    const getBrochureInfo = (projectSlug: string) => {
      switch (projectSlug) {
        case 'tilottoma':
          return {
            url: '/assets/images/pdf/SAL TILOTTOMA Brochure.pdf',
            filename: 'SAL_TILOTTOMA_Brochure.pdf'
          };
        case 'mollicks-dream':
          return {
            url: '/assets/images/pdf/MOLLICKS DREAM.pptx',
            filename: 'MOLLICKS_DREAM.pptx'
          };
        case 'haruns-nest':
          return {
            url: '/assets/images/pdf/HARUN\'S NEST BROCHURE.pdf',
            filename: 'HARUNS_NEST_BROCHURE.pdf'
          };
        default:
          return {
            url: '/assets/images/pdf/Rest of all SB brochure.pdf',
            filename: 'Soldiers_Builders_Brochure.pdf'
          };
      }
    };

    const brochureInfo = getBrochureInfo(projectId);
    const link = document.createElement('a');
    link.href = brochureInfo.url;
    link.download = brochureInfo.filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const cards: Card[] = [
    {
      title: "Interior",
      description: "Explore the luxurious interior designs and premium finishes",
      icon: Home,
      image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?q=80&w=2560&auto=format&fit=crop",
      buttonText: "View Interior",
      href: `/projects/${projectId}/interior`,
      action: () => navigate(`/projects/${projectId}/interior`)
    },
    {
      title: "Floor Plan",
      description: "Detailed floor plans and architectural layouts",
      icon: FileText,
      image: "https://images.unsplash.com/photo-1503387762-592deb58ef4e?q=80&w=2560&auto=format&fit=crop",
      buttonText: "View Plans",
      href: `/projects/${projectId}/floorplan`,
      action: () => navigate(`/projects/${projectId}/floorplan`)
    },
    {
      title: "Brochure",
      description: "Download comprehensive project brochure and details",
      icon: Download,
      image: "https://images.unsplash.com/photo-1554224155-6726b3ff858f?q=80&w=2560&auto=format&fit=crop",
      buttonText: "Download",
      href: "#",
      action: () => handleBrochureDownload(projectId)
    }
  ];

  return (
    <section className="py-12 bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-8"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Explore More
          </h2>
          <p className="text-xl text-gray-400 max-w-2xl mx-auto">
            Dive deeper into the project details with our comprehensive resources
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-6xl mx-auto">
          {cards.map((card, index) => (
            <motion.div
              key={card.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.2, duration: 0.8 }}
            >
              <CardContainer className="inter-var py-8">
                <CardBody className="bg-gray-50 relative group/card dark:hover:shadow-2xl dark:hover:shadow-emerald-500/[0.1] dark:bg-gray-800 dark:border-white/[0.2] border-black/[0.1] w-full max-w-sm h-auto rounded-xl p-4 border">
                  <CardItem
                    translateZ="50"
                    className="text-lg font-bold text-neutral-600 dark:text-white flex items-center space-x-2"
                  >
                    <card.icon className="h-5 w-5 text-yellow-400" />
                    <span>{card.title}</span>
                  </CardItem>
                  <CardItem
                    as="p"
                    translateZ="60"
                    className="text-neutral-500 text-xs max-w-sm mt-1 dark:text-neutral-300"
                  >
                    {card.description}
                  </CardItem>
                  <CardItem translateZ="100" className="w-full mt-3">
                    <img
                      src={card.image}
                      height="400"
                      width="400"
                      className="h-40 w-full object-cover rounded-lg group-hover/card:shadow-xl"
                      alt={card.title}
                    />
                  </CardItem>
                  <div className="flex justify-between items-center mt-6">
                    <CardItem
                      translateZ={20}
                      as={card.title === "Brochure" ? "button" : Link}
                      to={card.title === "Brochure" ? undefined : card.href}
                      onClick={card.title === "Brochure" ? card.action : undefined}
                      className="px-3 py-1 rounded-lg text-xs font-normal dark:text-white hover:text-yellow-400 transition-colors"
                    >
                      {card.title === "Brochure" ? "Download →" : "Learn more →"}
                    </CardItem>
                    <CardItem
                      translateZ={20}
                      as="button"
                      onClick={card.action}
                      className="px-3 py-1 rounded-lg bg-black dark:bg-white dark:text-black text-white text-xs font-bold hover:bg-yellow-400 hover:text-black transition-all duration-200"
                    >
                      {card.buttonText}
                    </CardItem>
                  </div>
                </CardBody>
              </CardContainer>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
