"use client";

import React from "react";
import { motion } from "framer-motion";

interface BoardMember {
  name: string;
  title: string;
  image: string;
  bio?: string;
  experience?: string;
  expertise?: string[];
}

interface BoardMembersRowGridProps {
  members: BoardMember[];
}

export function BoardMembersRowGrid({ members }: BoardMembersRowGridProps) {
  return (
    <div className="w-full h-full py-20">
      <h2 className="max-w-7xl pl-4 mx-auto text-xl md:text-5xl font-bold text-white font-sans mb-4">
        Meet Our Distinguished Board
      </h2>
      <p className="max-w-7xl pl-4 mx-auto text-lg text-gray-400 font-sans mb-8">
        Leaders who guide Soldiers Builders with military precision and vision
      </p>
      
      {/* Grid Layout - 4 per row */}
      <div className="max-w-7xl mx-auto px-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {members.map((member, index) => (
            <BoardMemberCard key={member.name} member={member} index={index} />
          ))}
        </div>
      </div>
    </div>
  );
}

const BoardMemberCard = ({ member, index }: { member: BoardMember; index: number }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
      className="group relative overflow-hidden rounded-3xl bg-gray-800 shadow-xl hover:shadow-2xl transition-all duration-300"
    >
      {/* Card Image */}
      <div className="relative overflow-hidden">
        <img
          src={member.image}
          alt={member.name}
          className="w-full object-cover transition-transform duration-300 group-hover:scale-105"
          style={{ height: '15rem' }} // 1.25x of 12rem (192px -> 240px)
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      </div>

      {/* Card Content */}
      <div className="p-4">
        <h3 className="text-lg font-bold text-white mb-1 group-hover:text-yellow-400 transition-colors duration-300">
          {member.name}
        </h3>
        <p className="text-sm text-gray-400 leading-relaxed">
          {member.title}
        </p>
      </div>

      {/* Hover Overlay with Tagline */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 flex flex-col justify-center items-center p-4">
        <div className="transform translate-y-4 group-hover:translate-y-0 transition-transform duration-300 text-center">
          <p className="text-lg text-white font-medium italic leading-relaxed">
            {getTaglineForMember(member.name)}
          </p>
        </div>
      </div>
    </motion.div>
  );
};

// Function to get nice taglines for each member
const getTaglineForMember = (name: string): string => {
  const taglines: { [key: string]: string } = {
    "Maj Toufikul Muneem": "Visionary leader shaping tomorrow's skylines",
    "Brig Gen Md Zobaidur Rahman": "Strategic excellence in every foundation",
    "Ipsha Nazia Adiba": "Innovation meets precision in design",
    "Dr AFM Zohurul Haque": "Building dreams with scientific precision",
    "Dr Istiaque Anwar": "Where expertise meets architectural excellence",
    "Dr Mosammat Dipa": "Transforming visions into reality",
    "Dr Sohel Akhter": "Excellence through dedication and expertise",
    "Dr. Shahana Begum": "Leading with wisdom and innovation",
    "Engr. Selima Nargis": "Engineering the future of construction",
    "Fihor Esrar Eham": "Precision and quality in every detail",
    "Lt Col Maksuda Begum": "Military discipline meets construction excellence",
    "Lt Col Md Billal Hossain": "Strategic planning for superior results",
    "Lt Col Md Emdadul Haque": "Leadership through service and dedication",
    "Lt Col SM Nazrul Islam": "Building with honor and integrity",
    "Mahfuzur R Chowdhury": "Excellence in execution and delivery",
    "Md Mafrul Haque": "Commitment to quality and innovation",
    "Md Selim Reza": "Crafting spaces that inspire living",
    "Md Zakir H Khan": "Dedication to architectural perfection",
    "Mg Kamrul H Khan": "Strategic vision for sustainable development",
    "Mg Md Mahbubur Rahman": "Leadership that builds lasting legacies",
    "Mosammat Halima Begum": "Nurturing excellence in every project",
    "Most. Sayeeda Akhter": "Inspiring innovation through leadership",
    "Prof A Sakur Khan": "Academic excellence meets practical wisdom",
    "Prof Begum Nasrin": "Knowledge and experience in perfect harmony",
    "Prof Md Amanullah": "Scholarly approach to construction excellence",
    "Prof Md Kamruzzaman": "Education and expertise building the future",
    "Shanara Begum": "Grace and strength in leadership",
    "Afrin Sultana": "Dedication to excellence and innovation",
    "Arm Momtajuddin": "Strategic thinking for superior outcomes",
    "Bg Md Delwar Hossain": "Military precision in construction leadership",
    "Bg Md Habibur Rahman": "Excellence through disciplined execution",
    "Col Mdmostafizur Rahman": "Honor and integrity in every project"
  };

  return taglines[name] || "Distinguished leadership in construction excellence";
};
