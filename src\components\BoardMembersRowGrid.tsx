"use client";

import React from "react";
import { motion } from "framer-motion";

interface BoardMember {
  name: string;
  title: string;
  image: string;
  bio?: string;
  experience?: string;
  expertise?: string[];
}

interface BoardMembersRowGridProps {
  members: BoardMember[];
}

export function BoardMembersRowGrid({ members }: BoardMembersRowGridProps) {
  return (
    <div className="w-full h-full py-20">
      <h2 className="max-w-7xl pl-4 mx-auto text-xl md:text-5xl font-bold text-white font-sans mb-4">
        Meet Our Distinguished Board
      </h2>
      <p className="max-w-7xl pl-4 mx-auto text-lg text-gray-400 font-sans mb-8">
        Leaders who guide Soldiers Builders with military precision and vision
      </p>
      
      {/* Grid Layout - 4 per row */}
      <div className="max-w-7xl mx-auto px-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {members.map((member, index) => (
            <BoardMemberCard key={member.name} member={member} index={index} />
          ))}
        </div>
      </div>
    </div>
  );
}

const BoardMemberCard = ({ member, index }: { member: BoardMember; index: number }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
      className="group relative overflow-hidden rounded-3xl bg-gray-800 shadow-xl hover:shadow-2xl transition-all duration-300"
    >
      {/* Card Image */}
      <div className="relative overflow-hidden">
        <img
          src={member.image}
          alt={member.name}
          className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105"
          style={{ height: '12rem' }} // 0.75x of original 16rem (256px -> 192px)
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      </div>

      {/* Card Content */}
      <div className="p-4">
        <h3 className="text-lg font-bold text-white mb-1 group-hover:text-yellow-400 transition-colors duration-300">
          {member.name}
        </h3>
        <p className="text-sm text-gray-400 leading-relaxed">
          {member.title}
        </p>
      </div>

      {/* Hover Overlay with Details */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 flex flex-col justify-end p-4">
        <div className="transform translate-y-4 group-hover:translate-y-0 transition-transform duration-300">
          <h3 className="text-lg font-bold text-white mb-2">
            {member.name}
          </h3>
          <p className="text-sm text-yellow-400 font-semibold mb-3">
            {member.title}
          </p>
          
          {member.bio && (
            <p className="text-xs text-gray-300 leading-relaxed line-clamp-3">
              {member.bio}
            </p>
          )}
          
          {!member.bio && (
            <p className="text-xs text-gray-300 leading-relaxed">
              Distinguished leader bringing extensive experience and strategic vision to guide Soldiers Builders.
            </p>
          )}
          
          {member.expertise && member.expertise.length > 0 && (
            <div className="mt-2 flex flex-wrap gap-1">
              {member.expertise.slice(0, 2).map((skill, idx) => (
                <span
                  key={idx}
                  className="px-2 py-1 bg-yellow-400/20 text-yellow-300 rounded-full text-xs font-medium"
                >
                  {skill}
                </span>
              ))}
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
};
