import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Handshake, TrendingUp, Shield, Users, CheckCircle, ArrowRight, Send, MapPin, Phone, Mail } from 'lucide-react';

const Landowners = () => {
  const [formData, setFormData] = useState({
    landownerName: '',
    email: '',
    phone: '',
    propertyLocation: '',
    landSize: '',
    propertyType: '',
    developmentInterest: [],
    additionalInfo: '',
    preferredContact: 'email'
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Compose email body with all form data
    const emailBody = `
Landowner Partnership Inquiry from Soldiers Builders Website

Landowner Name: ${formData.landownerName}
Email: ${formData.email}
Phone: ${formData.phone}
Property Location: ${formData.propertyLocation}
Land Size: ${formData.landSize}
Property Type: ${formData.propertyType}
Development Interest: ${formData.developmentInterest.join(', ')}
Preferred Contact Method: ${formData.preferredContact}

Additional Information:
${formData.additionalInfo}

---
This inquiry was sent from the Soldiers Builders Landowner page.
    `.trim();

    // Create Gmail compose URL
    const gmailUrl = `https://mail.google.com/mail/?view=cm&to=<EMAIL>&su=${encodeURIComponent(`Landowner Inquiry - ${formData.landownerName}`)}&body=${encodeURIComponent(emailBody)}`;

    // Open Gmail in a new tab
    window.open(gmailUrl, '_blank');
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      developmentInterest: checked
        ? [...prev.developmentInterest, value]
        : prev.developmentInterest.filter(item => item !== value)
    }));
  };

  const benefits = [
    {
      icon: TrendingUp,
      title: "Maximize Land Value",
      description: "Transform your land into a premium residential development with guaranteed returns"
    },
    {
      icon: Shield,
      title: "Risk-Free Partnership",
      description: "No upfront investment required from your side - we handle all development costs"
    },
    {
      icon: Users,
      title: "Expert Team",
      description: "Work with our experienced team of architects, engineers, and construction professionals"
    },
    {
      icon: Handshake,
      title: "Transparent Process",
      description: "Clear agreements, regular updates, and fair profit sharing throughout the project"
    }
  ];

  const processSteps = [
    {
      step: "01",
      title: "Site Visit & Assessment",
      description: "Our team conducts a thorough evaluation of your land including location, size, accessibility, and development potential."
    },
    {
      step: "02",
      title: "Detailed Proposal & Terms",
      description: "We prepare a comprehensive proposal with project details, timelines, and profit-sharing arrangements."
    },
    {
      step: "03",
      title: "Joint Venture Agreement",
      description: "Legal documentation and agreement signing with transparent terms and conditions."
    },
    {
      step: "04",
      title: "Construction & Development",
      description: "We handle all aspects of construction, from design to completion, with regular progress updates."
    },
    {
      step: "05",
      title: "Handover & Profit Sharing",
      description: "Upon completion, you receive your share of apartments or equivalent value as agreed."
    }
  ];

  const requirements = [
    "Clear title and ownership documents",
    "Minimum 3 katha land size",
    "Accessible location with proper road connectivity",
    "Suitable for residential development",
    "Free from legal disputes",
    "Proper utility connections available"
  ];

  const testimonials = [
    {
      name: "Abdul Karim",
      location: "Jolshiri Abashon",
      image: "https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=150",
      content: "Partnering with Soldiers Builders was the best decision for my land. They handled everything professionally and delivered beyond expectations. The profit sharing was fair and transparent."
    },
    {
      name: "Fatima Begum",
      location: "Chondrima Model Town",
      image: "https://images.pexels.com/photos/1181424/pexels-photo-1181424.jpeg?auto=compress&cs=tinysrgb&w=150",
      content: "I was impressed by their military precision and attention to detail. The project was completed on time and the quality exceeded my expectations. Highly recommended for land development."
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-48 bg-gradient-to-r from-gray-900 to-gray-800">
        <div className="absolute inset-0">
          <img
            src="/assets/images/projects/File/vcbv.png"
            alt="Landowner Partnership Background"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-black/40"></div>
        </div>
      </section>

      {/* Title and Subtitle after Hero Section */}
      <section className="bg-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-black mb-6">
            Landowner <span className="text-green-800">Partnership</span>
          </h1>
          <p className="text-xl text-gray-700 max-w-3xl mx-auto">
            Transform your land into a premium residential development with guaranteed returns and military precision
          </p>
        </div>
      </section>

      {/* Why Partner With Us */}
      <section className="py-20 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Why Partner With Soldiers Builders?
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Join successful landowners who have maximized their land value through our expert development partnership since 2008
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -5 }}
                className="bg-gray-800 p-6 rounded-xl text-center hover:bg-gray-700 transition-all duration-300"
              >
                <div className="bg-yellow-400 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                  <benefit.icon className="h-8 w-8 text-gray-900" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-4">{benefit.title}</h3>
                <p className="text-gray-400">{benefit.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-20 bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              How It Works
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Our streamlined process ensures a smooth partnership from start to finish
            </p>
          </motion.div>

          <div className="space-y-8">
            {processSteps.map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className={`flex flex-col md:flex-row items-center gap-8 ${
                  index % 2 === 1 ? 'md:flex-row-reverse' : ''
                }`}
              >
                <div className="flex-1 bg-gray-900 rounded-xl p-8">
                  <div className="flex items-center mb-4">
                    <div className="bg-yellow-400 text-gray-900 text-2xl font-bold w-16 h-16 rounded-full flex items-center justify-center mr-4">
                      {step.step}
                    </div>
                    <h3 className="text-2xl font-bold text-white">{step.title}</h3>
                  </div>
                  <p className="text-gray-400 leading-relaxed">{step.description}</p>
                </div>
                <div className="flex-shrink-0">
                  <ArrowRight className="h-12 w-12 text-yellow-400 rotate-90 md:rotate-0" />
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Requirements */}
      <section className="py-20 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
                Land Requirements
              </h2>
              <p className="text-xl text-gray-400 mb-8">
                We work with landowners whose properties meet certain criteria for successful development
              </p>
              <ul className="space-y-4">
                {requirements.map((requirement, index) => (
                  <motion.li
                    key={index}
                    initial={{ opacity: 0, x: -10 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-start space-x-3"
                  >
                    <CheckCircle className="h-6 w-6 text-yellow-400 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-300">{requirement}</span>
                  </motion.li>
                ))}
              </ul>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="relative"
            >
              <img
                src="https://images.pexels.com/photos/323780/pexels-photo-323780.jpeg?auto=compress&cs=tinysrgb&w=800"
                alt="Land Development"
                className="w-full h-96 object-cover rounded-xl shadow-2xl"
              />
              <div className="absolute inset-0 bg-gradient-to-tr from-yellow-400/20 to-transparent rounded-xl"></div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Success Stories */}
      <section className="py-20 bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Success Stories
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Hear from landowners who have successfully partnered with us
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-gray-900 p-8 rounded-xl"
              >
                <div className="flex items-center mb-6">
                  <img
                    src={testimonial.image}
                    alt={testimonial.name}
                    className="w-16 h-16 rounded-full mr-4 object-cover"
                  />
                  <div>
                    <h3 className="text-white font-bold text-lg">{testimonial.name}</h3>
                    <p className="text-gray-400">{testimonial.location}</p>
                  </div>
                </div>
                <p className="text-gray-300 leading-relaxed">
                  "{testimonial.content}"
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Financial Benefits */}
      <section className="py-20 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Financial Benefits
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Understanding the financial advantages of partnering with Soldiers Builders
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-gray-800 p-8 rounded-xl text-center"
            >
              <div className="text-4xl font-bold text-yellow-400 mb-4">40-60%</div>
              <h3 className="text-xl font-semibold text-white mb-4">Profit Share</h3>
              <p className="text-gray-400">
                Landowners typically receive 40-60% of the developed units or equivalent value
              </p>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-gray-800 p-8 rounded-xl text-center"
            >
              <div className="text-4xl font-bold text-yellow-400 mb-4">0 TK</div>
              <h3 className="text-xl font-semibold text-white mb-4">Your Investment</h3>
              <p className="text-gray-400">
                No upfront investment required - we handle all development costs
              </p>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-gray-800 p-8 rounded-xl text-center"
            >
              <div className="text-4xl font-bold text-yellow-400 mb-4">300-500%</div>
              <h3 className="text-xl font-semibold text-white mb-4">Value Increase</h3>
              <p className="text-gray-400">
                Typical land value increase through premium development
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="py-20 bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Connect With Us
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Ready to explore partnership opportunities? Fill out the form below and our team will contact you within 24 hours
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-gray-900 rounded-xl p-8"
            >
              <h3 className="text-2xl font-bold text-white mb-6">Partnership Inquiry Form</h3>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label htmlFor="landownerName" className="block text-sm font-medium text-gray-300 mb-2">
                    Landowner Name *
                  </label>
                  <input
                    type="text"
                    id="landownerName"
                    name="landownerName"
                    value={formData.landownerName}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-green-400 transition-colors duration-200"
                    placeholder="Enter your full name"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      required
                      className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-green-400 transition-colors duration-200"
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-300 mb-2">
                      Phone Number *
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      required
                      className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-green-400 transition-colors duration-200"
                      placeholder="+880 1XXX-XXXXXX"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="propertyLocation" className="block text-sm font-medium text-gray-300 mb-2">
                    Property Location/Address *
                  </label>
                  <input
                    type="text"
                    id="propertyLocation"
                    name="propertyLocation"
                    value={formData.propertyLocation}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-green-400 transition-colors duration-200"
                    placeholder="Enter complete property address"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="landSize" className="block text-sm font-medium text-gray-300 mb-2">
                      Land Size
                    </label>
                    <input
                      type="text"
                      id="landSize"
                      name="landSize"
                      value={formData.landSize}
                      onChange={handleChange}
                      className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-green-400 transition-colors duration-200"
                      placeholder="e.g., 5 Katha, 3600 SFT"
                    />
                  </div>
                  <div>
                    <label htmlFor="propertyType" className="block text-sm font-medium text-gray-300 mb-2">
                      Property Type
                    </label>
                    <select
                      id="propertyType"
                      name="propertyType"
                      value={formData.propertyType}
                      onChange={handleChange}
                      className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-green-400 transition-colors duration-200"
                    >
                      <option value="">Select property type</option>
                      <option value="residential">Residential</option>
                      <option value="commercial">Commercial</option>
                      <option value="mixed-use">Mixed-use</option>
                      <option value="agricultural">Agricultural</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-3">
                    Development Interest (Select all that apply)
                  </label>
                  <div className="space-y-2">
                    {['Sale', 'Joint Venture', 'Development Partnership'].map((option) => (
                      <label key={option} className="flex items-center">
                        <input
                          type="checkbox"
                          value={option}
                          checked={formData.developmentInterest.includes(option)}
                          onChange={handleCheckboxChange}
                          className="mr-3 h-4 w-4 text-green-600 focus:ring-green-500 border-gray-600 rounded bg-gray-800"
                        />
                        <span className="text-gray-300">{option}</span>
                      </label>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-3">
                    Preferred Contact Method
                  </label>
                  <div className="flex space-x-6">
                    {[
                      { value: 'email', label: 'Email' },
                      { value: 'phone', label: 'Phone' },
                      { value: 'whatsapp', label: 'WhatsApp' }
                    ].map((option) => (
                      <label key={option.value} className="flex items-center">
                        <input
                          type="radio"
                          name="preferredContact"
                          value={option.value}
                          checked={formData.preferredContact === option.value}
                          onChange={handleChange}
                          className="mr-2 h-4 w-4 text-green-600 focus:ring-green-500 border-gray-600 bg-gray-800"
                        />
                        <span className="text-gray-300">{option.label}</span>
                      </label>
                    ))}
                  </div>
                </div>

                <div>
                  <label htmlFor="additionalInfo" className="block text-sm font-medium text-gray-300 mb-2">
                    Additional Information
                  </label>
                  <textarea
                    id="additionalInfo"
                    name="additionalInfo"
                    value={formData.additionalInfo}
                    onChange={handleChange}
                    rows={4}
                    className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-green-400 transition-colors duration-200"
                    placeholder="Tell us more about your property, development goals, or any specific requirements..."
                  />
                </div>

                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  type="submit"
                  className="w-full bg-green-800 text-white py-3 rounded-lg font-semibold hover:bg-green-700 transition-all duration-200 flex items-center justify-center space-x-2"
                >
                  <Send className="h-5 w-5" />
                  <span>Submit Partnership Inquiry</span>
                </motion.button>
              </form>
            </motion.div>

            {/* Contact Information */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="space-y-8"
            >
              <div className="bg-gray-900 rounded-xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6">Get In Touch</h3>
                <div className="space-y-6">
                  <div className="flex items-start space-x-4">
                    <div className="bg-green-800 p-3 rounded-lg">
                      <Phone className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-white">Call Us</h4>
                      <p className="text-gray-400">+880 1769-100680</p>
                      <p className="text-gray-400">+880 1711-164217</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="bg-green-800 p-3 rounded-lg">
                      <Mail className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-white">Email Us</h4>
                      <p className="text-gray-400"><EMAIL></p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="bg-green-800 p-3 rounded-lg">
                      <MapPin className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-white">Visit Us</h4>
                      <p className="text-gray-400">Plot 16, Road 504H, Sector 16, Jolshiri Abashon, Dhaka</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-900 rounded-xl p-8">
                <h3 className="text-xl font-bold text-white mb-4">Why Choose Soldiers Builders?</h3>
                <ul className="space-y-3 text-gray-400">
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <span>Proven track record since 2008</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <span>Military precision in project execution</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <span>Transparent communication throughout</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <span>Maximum return on investment</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <span>Expert regulatory approval assistance</span>
                  </li>
                </ul>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-green-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white">
            Ready to Partner with Us?
          </h2>
          <p className="text-xl text-white max-w-2xl mx-auto">
            Let's discuss how we can transform your land into a profitable investment
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mt-8">
            <button className="bg-white text-green-800 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-all duration-200">
              🏗️ Schedule Site Visit
            </button>
            <button className="bg-white text-green-800 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-all duration-200">
              💼 Get Free Consultation
            </button>
            <button className="bg-white text-green-800 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-all duration-200">
              📊 Download Partnership Guide
            </button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Landowners;