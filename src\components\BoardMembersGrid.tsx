import React from 'react';
import { motion } from 'framer-motion';

interface BoardMember {
  name: string;
  title: string;
  image: string;
  bio?: string;
  experience?: string;
  expertise?: string[];
}

interface BoardMembersGridProps {
  members: BoardMember[];
}

export function BoardMembersGrid({ members }: BoardMembersGridProps) {
  return (
    <div className="w-full py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          className="text-center mb-16"
        >
          <div className="flex items-center justify-center mb-6">
            <div className="flex space-x-2">
              <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
              <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
              <div className="w-3 h-3 bg-green-400 rounded-full"></div>
              <div className="w-3 h-3 bg-blue-400 rounded-full"></div>
              <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
            </div>
          </div>
          <h2 className="text-xl text-gray-400 font-medium mb-2 tracking-wider uppercase">
            BOARD OF
          </h2>
          <h1 className="text-4xl md:text-6xl font-bold text-blue-600 mb-4">
            DIRECTORS
          </h1>
          <p className="text-sm text-gray-500 uppercase tracking-wider">
            AS OF MARCH 1, 2019
          </p>
        </motion.div>

        {/* Board Members Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {members.map((member, index) => (
            <motion.div
              key={member.name}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="text-center group"
            >
              {/* Member Photo */}
              <div className="relative mb-6 overflow-hidden">
                <img
                  src={member.image}
                  alt={member.name}
                  className="w-full h-64 object-cover transition-transform duration-300 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>

              {/* Member Info */}
              <div className="space-y-2">
                <h3 className="text-lg font-bold text-gray-900 dark:text-white uppercase tracking-wide">
                  {member.name}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
                  {member.title}
                </p>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Footer */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          className="text-center mt-16"
        >
          <div className="flex items-center justify-center space-x-4 text-gray-400">
            <div className="w-8 h-8 bg-gray-200 rounded"></div>
            <p className="text-sm uppercase tracking-wider">
              SOLDIERS BUILDERS COMPANY, INC. | 2024 ANNUAL REPORT
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
