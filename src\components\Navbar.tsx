import React, { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { Menu, X, Shield, ChevronDown } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import ThemeToggle from './ThemeToggle';
import DockNavbar from './DockNavbar';
import { useTheme } from '../contexts/ThemeContext';

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [hoveredSegment, setHoveredSegment] = useState<string | null>(null);
  const location = useLocation();
  const navigate = useNavigate();
  const { isDarkMode } = useTheme();

  // Function to handle smooth scrolling to sections
  const handleSectionScroll = (href: string) => {
    if (href.includes('#')) {
      const [path, hash] = href.split('#');
      if (location.pathname === path) {
        // If we're already on the page, scroll to the section
        const element = document.getElementById(hash);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' });
        }
      } else {
        // If we're on a different page, navigate first and then scroll
        navigate(path);
        // Use multiple attempts to find the element after navigation
        const scrollToElement = (attempts = 0) => {
          const element = document.getElementById(hash);
          if (element) {
            element.scrollIntoView({ behavior: 'smooth' });
          } else if (attempts < 10) {
            // Retry up to 10 times with increasing delays
            setTimeout(() => scrollToElement(attempts + 1), 100 * (attempts + 1));
          }
        };
        // Start the first attempt after a short delay
        setTimeout(() => scrollToElement(), 100);
      }
    }
  };

  const navigation = [
    { name: 'Home', href: '/' },
    { 
      name: 'About Us', 
      href: '/about',
      subsegments: [
        { name: 'Who We Are', href: '/about#who-we-are' },
        { name: 'Mission & Vision', href: '/about#mission-vision' },
        { name: 'Message from MD & Chairman', href: '/about#message' },
        { name: 'Board of Directors', href: '/about#board' }
      ]
    },
    { 
      name: 'Projects', 
      href: '/projects',
      subsegments: [
        { name: 'Ongoing', href: '/projects#ongoing' },
        { name: 'Upcoming', href: '/projects#upcoming' },
        { name: 'Completed', href: '/projects#completed' }
      ]
    },
    { 
      name: 'Services', 
      href: '/services',
      subsegments: [
        { name: 'Construction', href: '/services#construction' },
        { name: 'Interior Design', href: '/services#interior' },
        { name: 'Consultation', href: '/services#consultation' }
      ]
    },
    { name: 'Standard Features', href: '/standard-features' },
    {
      name: 'Gallery',
      href: '/gallery',
      subsegments: [
        { name: 'Interior', href: '/gallery#interior' },
        { name: 'Exterior', href: '/gallery#exterior' },
        { name: 'Construction', href: '/gallery#projects' },
        { name: 'Project Launch Events', href: '/gallery#events' },
        { name: 'Signing New Deals', href: '/gallery#deals' }
      ]
    },
    { name: 'Landowners', href: '/landowners' },
    { name: 'Contact', href: '/contact' },
  ];

  return (
    <nav className="bg-black/70 backdrop-blur-sm border-b border-gray-800/60 fixed top-0 left-0 w-full z-50">
      {/* Main Navigation */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-3">
            <motion.div
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.98 }}
              className="flex items-center space-x-3"
              transition={{ type: "spring", stiffness: 200, damping: 15 }}
            >
              <img
                src={isDarkMode ? "/assets/images/20250727_1542_White Logo Design_remix_01k15m0vqkf7j938wyjnc618af.png" : "/assets/images/SB_Logo_white_background-removebg-preview copy.png"}
                alt="Soldiers Builders Logo"
                className={`w-auto object-contain drop-shadow ${isDarkMode ? 'h-16' : 'h-12'}`}
              />
              <div className="flex flex-col">
                <h1 className="text-lg font-bold text-white whitespace-nowrap">Soldiers Builders</h1>
                <p className="text-xs text-gray-300 whitespace-nowrap">We build your dreams</p>
              </div>
            </motion.div>
          </Link>

          {/* Desktop Navigation with Dock Effect */}
          <div className="hidden md:flex items-center space-x-4">
            <DockNavbar
              navigation={navigation}
              spring={{ mass: 0.1, stiffness: 200, damping: 15 }}
              magnification={1.1}
              distance={80}
              baseItemSize={40}
              hoveredSegment={hoveredSegment}
              setHoveredSegment={setHoveredSegment}
              handleSectionScroll={handleSectionScroll}
              className="flex items-center space-x-1"
            />

            {/* Theme Toggle */}
            <motion.div
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
              className="ml-2"
            >
              <ThemeToggle />
            </motion.div>

            <Link to="/downloads">
              <motion.button
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="bg-green-800 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-green-700 transition-all duration-200 shadow-lg hover:shadow-xl whitespace-nowrap"
              >
                Downloads
              </motion.button>
            </Link>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center space-x-2">
            <ThemeToggle />
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="text-gray-300 hover:text-green-400 transition-colors duration-200"
            >
              {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="md:hidden border-t border-gray-800 absolute top-full left-0 right-0 z-50"
          >
            <div className="max-h-[calc(100vh-4rem)] overflow-y-auto px-2 pt-2 pb-3 space-y-1 bg-black/95 backdrop-blur-sm shadow-xl">
              {navigation.map((item, index) => (
                <motion.div
                  key={item.name}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                >
                  <Link
                    to={item.href}
                    className={`block px-3 py-2 text-sm font-medium transition-all duration-200 rounded-lg ${
                      location.pathname === item.href
                        ? 'text-green-400 bg-green-400/10 border-l-2 border-green-400'
                        : 'text-gray-300 hover:text-green-400 hover:bg-gray-700/50 hover:translate-x-1'
                    }`}
                    onClick={() => setIsOpen(false)}
                  >
                    {item.name}
                  </Link>
                  {/* Mobile subsegments */}
                  {item.subsegments && (
                    <motion.div 
                      className="ml-4 mt-1 space-y-1"
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      transition={{ duration: 0.2 }}
                    >
                      {item.subsegments.map((subsegment, subIndex) => (
                        <motion.div
                          key={subsegment.name}
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: subIndex * 0.05 }}
                        >
                          {subsegment.href.includes('#') ? (
                            <button
                              onClick={() => {
                                handleSectionScroll(subsegment.href);
                                setIsOpen(false);
                              }}
                              className="block w-full text-left px-3 py-1 text-sm text-gray-400 hover:text-green-400 hover:bg-gray-700/30 rounded transition-all duration-200"
                            >
                              • {subsegment.name}
                            </button>
                          ) : (
                            <Link
                              to={subsegment.href}
                              className="block px-3 py-1 text-sm text-gray-400 hover:text-green-400 hover:bg-gray-700/30 rounded transition-all duration-200"
                              onClick={() => setIsOpen(false)}
                            >
                              • {subsegment.name}
                            </Link>
                          )}
                        </motion.div>
                      ))}
                    </motion.div>
                  )}
                </motion.div>
              ))}
              <motion.div
                className="px-3 py-2"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: navigation.length * 0.05 }}
              >
                <Link to="/downloads" className="w-full">
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="w-full bg-gradient-to-r from-yellow-400 to-yellow-600 text-gray-900 px-4 py-2 rounded-lg font-medium hover:from-yellow-500 hover:to-yellow-700 transition-all duration-200 shadow-lg"
                  >
                    Downloads
                  </motion.button>
                </Link>
              </motion.div>
            </div>
          </motion.div>
        )}
      </div>
    </nav>
  );
};

export default Navbar;