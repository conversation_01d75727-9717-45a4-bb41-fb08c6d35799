import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronRight, ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';

interface Project {
  id: number;
  name: string;
  slug: string;
  location: string;
  image: string;
  description: string;
  size: string;
  bedrooms: number;
  status: string;
}

interface FeaturedProjectsSlideshowProps {
  featuredProjects: Project[];
}

const FeaturedProjectsSlideshow: React.FC<FeaturedProjectsSlideshowProps> = ({ featuredProjects }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [direction, setDirection] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const [progress, setProgress] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const progressRef = useRef<NodeJS.Timeout | null>(null);

  // Auto-slide functionality with progress
  useEffect(() => {
    if (intervalRef.current) clearInterval(intervalRef.current);
    if (progressRef.current) clearInterval(progressRef.current);

    if (!isPaused) {
      setProgress(0);

      // Progress bar animation
      progressRef.current = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 100) {
            return 0;
          }
          return prev + 2; // 2% every 100ms = 5 seconds total
        });
      }, 100);

      // Slide change
      intervalRef.current = setInterval(() => {
        setDirection(1);
        setCurrentIndex((prev) => (prev + 1) % featuredProjects.length);
        setProgress(0);
      }, 5000); // 5 seconds per slide
    }

    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
      if (progressRef.current) clearInterval(progressRef.current);
    };
  }, [featuredProjects.length, isPaused, currentIndex]);

  const goToSlide = (index: number) => {
    setDirection(index > currentIndex ? 1 : -1);
    setCurrentIndex(index);
    setProgress(0);
  };

  const goToPrevious = () => {
    setDirection(-1);
    setCurrentIndex((prev) => (prev - 1 + featuredProjects.length) % featuredProjects.length);
    setProgress(0);
  };

  const goToNext = () => {
    setDirection(1);
    setCurrentIndex((prev) => (prev + 1) % featuredProjects.length);
    setProgress(0);
  };

  const slideVariants = {
    initial: (direction: number) => ({
      x: direction > 0 ? 1200 : -1200,
      opacity: 0,
      scale: 0.9,
      rotateY: direction > 0 ? 15 : -15,
    }),
    animate: {
      x: 0,
      opacity: 1,
      scale: 1,
      rotateY: 0,
      transition: {
        duration: 1,
        ease: [0.25, 0.46, 0.45, 0.94],
        staggerChildren: 0.1,
      },
    },
    exit: (direction: number) => ({
      x: direction < 0 ? 1200 : -1200,
      opacity: 0,
      scale: 0.9,
      rotateY: direction < 0 ? 15 : -15,
      transition: {
        duration: 1,
        ease: [0.25, 0.46, 0.45, 0.94],
      },
    }),
  };

  const contentVariants = {
    initial: {
      opacity: 0,
      y: 50,
      scale: 0.9,
    },
    animate: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.8,
        delay: 0.3,
        ease: "easeOut",
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    initial: {
      opacity: 0,
      y: 30,
    },
    animate: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.key === 'ArrowLeft') {
        goToPrevious();
      } else if (event.key === 'ArrowRight') {
        goToNext();
      } else if (event.key >= '1' && event.key <= '9') {
        const index = parseInt(event.key) - 1;
        if (index < featuredProjects.length) {
          goToSlide(index);
        }
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [featuredProjects.length]);

  const currentProject = featuredProjects[currentIndex];

  return (
    <section className="py-20 bg-gray-900 overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Featured Projects
          </h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto">
            Discover our premium residential developments that redefine luxury living
          </p>
        </motion.div>

        {/* Slideshow Container */}
        <div
          className="relative"
          onMouseEnter={() => setIsPaused(true)}
          onMouseLeave={() => setIsPaused(false)}
        >
          {/* Main Slideshow */}
          <div className="relative h-[600px] md:h-[700px] rounded-3xl overflow-hidden shadow-2xl">
            <AnimatePresence initial={false} custom={direction} mode="wait">
              <motion.div
                key={currentIndex}
                custom={direction}
                variants={slideVariants}
                initial="initial"
                animate="animate"
                exit="exit"
                className="absolute inset-0"
              >
                <Link to={`/projects/${currentProject.slug}`} className="block h-full">
                  <div className="relative h-full group cursor-pointer">
                    {/* Project Image */}
                    <img
                      src={currentProject.image}
                      alt={currentProject.name}
                      className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
                    />

                    {/* Gradient Overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/40 to-transparent" />

                    {/* Project Info Overlay */}
                    <div className="absolute bottom-0 left-0 right-0 p-8 md:p-12">
                      <motion.div
                        variants={contentVariants}
                        initial="initial"
                        animate="animate"
                        className="space-y-4"
                      >
                        {/* Project Name */}
                        <motion.h3
                          variants={itemVariants}
                          className="text-4xl md:text-6xl font-bold text-white mb-4 leading-tight drop-shadow-lg"
                        >
                          {currentProject.name}
                        </motion.h3>

                        {/* Location */}
                        <motion.p
                          variants={itemVariants}
                          className="text-lg md:text-xl text-gray-200 mb-4 drop-shadow-md"
                        >
                          {currentProject.location}
                        </motion.p>

                        {/* Description */}
                        <motion.p
                          variants={itemVariants}
                          className="text-base md:text-lg text-gray-300 max-w-2xl mb-6 drop-shadow-md"
                        >
                          {currentProject.description}
                        </motion.p>

                        {/* Project Details */}
                        <motion.div
                          variants={itemVariants}
                          className="flex flex-wrap gap-6 mb-6"
                        >
                          <div className="flex items-center space-x-2 bg-black/30 px-4 py-2 rounded-lg backdrop-blur-sm">
                            <span className="text-yellow-400 font-semibold text-lg">{currentProject.size}</span>
                            <span className="text-gray-300">Size</span>
                          </div>
                          <div className="flex items-center space-x-2 bg-black/30 px-4 py-2 rounded-lg backdrop-blur-sm">
                            <span className="text-yellow-400 font-semibold text-lg">{currentProject.bedrooms}</span>
                            <span className="text-gray-300">Bedrooms</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className={`px-4 py-2 rounded-lg text-sm font-medium backdrop-blur-sm ${
                              currentProject.status === 'Completed'
                                ? 'bg-green-600/80 text-white'
                                : 'bg-blue-600/80 text-white'
                            }`}>
                              {currentProject.status}
                            </span>
                          </div>
                        </motion.div>

                        {/* CTA Button */}
                        <motion.div
                          variants={itemVariants}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          className="inline-flex items-center space-x-2 bg-gradient-to-r from-yellow-400 to-yellow-600 text-gray-900 px-8 py-3 rounded-lg font-semibold hover:from-yellow-500 hover:to-yellow-700 transition-all duration-200 shadow-lg"
                        >
                          <span>Explore Project</span>
                          <ArrowRight className="h-5 w-5" />
                        </motion.div>
                      </motion.div>
                    </div>
                  </div>
                </Link>
              </motion.div>
            </AnimatePresence>
          </div>

          {/* Navigation Arrows */}
          <button
            onClick={goToPrevious}
            aria-label="Previous project"
            className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/60 hover:bg-black/80 text-white p-4 rounded-full transition-all duration-200 z-10 backdrop-blur-sm shadow-lg hover:scale-110 focus:outline-none focus:ring-2 focus:ring-yellow-400"
          >
            <ChevronLeft className="h-6 w-6" />
          </button>
          <button
            onClick={goToNext}
            aria-label="Next project"
            className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/60 hover:bg-black/80 text-white p-4 rounded-full transition-all duration-200 z-10 backdrop-blur-sm shadow-lg hover:scale-110 focus:outline-none focus:ring-2 focus:ring-yellow-400"
          >
            <ChevronRight className="h-6 w-6" />
          </button>

          {/* Progress Bar */}
          <div className="absolute bottom-16 left-1/2 transform -translate-x-1/2 w-64 h-1 bg-white/20 rounded-full z-10">
            <motion.div
              className="h-full bg-yellow-400 rounded-full"
              style={{ width: `${progress}%` }}
              transition={{ duration: 0.1 }}
            />
          </div>

          {/* Slide Indicators */}
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-3 z-10">
            {featuredProjects.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-4 h-4 rounded-full transition-all duration-300 border-2 ${
                  index === currentIndex
                    ? 'bg-yellow-400 border-yellow-400 scale-125'
                    : 'bg-white/30 border-white/50 hover:bg-white/60 hover:border-white/80'
                }`}
              />
            ))}
          </div>
        </div>

        {/* Thumbnail Navigation */}
        <div className="mt-8 grid grid-cols-2 md:grid-cols-4 gap-4">
          {featuredProjects.map((project, index) => (
            <motion.button
              key={project.id}
              onClick={() => goToSlide(index)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className={`relative h-24 md:h-32 rounded-xl overflow-hidden transition-all duration-300 ${
                index === currentIndex
                  ? 'ring-4 ring-yellow-400 shadow-lg'
                  : 'ring-2 ring-gray-600 hover:ring-gray-400'
              }`}
            >
              <img
                src={project.image}
                alt={project.name}
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
              <div className="absolute bottom-2 left-2 right-2">
                <h4 className="text-white text-sm font-semibold truncate">
                  {project.name}
                </h4>
              </div>
              {index === currentIndex && (
                <div className="absolute inset-0 bg-yellow-400/20" />
              )}
            </motion.button>
          ))}
        </div>

        {/* View All Projects Button */}
        <div className="text-center mt-12">
          <Link to="/projects">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="border-2 border-yellow-400 text-yellow-400 px-8 py-3 rounded-lg font-semibold hover:bg-yellow-400 hover:text-gray-900 transition-all duration-200 flex items-center space-x-2 mx-auto"
            >
              <span>View All Projects</span>
              <ArrowRight className="h-5 w-5" />
            </motion.button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default FeaturedProjectsSlideshow;
